<?php

namespace App\Actions\Places;

use App\Enums\FavoriteType;
use App\Models\Place;
use App\Models\PlaceFavorite;
use App\Models\User;

class TogglePlaceFavoriteAction
{
    /**
     * Toggle favorite status for a place
     *
     * @param User $user
     * @param Place $place
     * @param FavoriteType|null $favoriteType
     * @return bool
     */
    /** @todo need to do refactor and also need to enhance data tracking for favorite */
    public function execute(User $user, Place $place, ?FavoriteType $favoriteType = null): bool
    {
        // Check for existing favorite including soft deleted ones
        $favorite = PlaceFavorite::withTrashed()
            ->where('user_id', $user->id)
            ->where('place_id', $place->id)
            ->first();

        if ($favorite) {
            // If the favorite is soft deleted, restore it
            if ($favorite->trashed()) {
                $favorite->restore();
                $favorite->type = $favoriteType ?? FavoriteType::PLANNED;
                $favorite->save();
                return true;
            }

            // If favorite exists and is not soft deleted
            if ($favorite->type === $favoriteType || $favoriteType === null) {
                $favorite->delete();
                return false;
            } else {
                $favorite->type = $favoriteType;
                $favorite->save();
                return true;
            }
        } else {
            // No existing favorite (including soft deleted), create new one
            PlaceFavorite::create([
                'user_id' => $user->id,
                'place_id' => $place->id,
                'type' => $favoriteType ?? FavoriteType::PLANNED,
            ]);
            return true;
        }
    }
}
