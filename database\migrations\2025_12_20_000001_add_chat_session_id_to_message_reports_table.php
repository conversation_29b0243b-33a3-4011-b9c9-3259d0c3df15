<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('message_reports', function (Blueprint $table) {
            // Add chat_session_id column
            $table->foreignId('chat_session_id')->nullable()->after('chat_message_id')->constrained()->onDelete('cascade');
            
            // Make chat_message_id nullable for general reports
            $table->foreignId('chat_message_id')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('message_reports', function (Blueprint $table) {
            $table->dropForeign(['chat_session_id']);
            $table->dropColumn('chat_session_id');
            
            // Revert chat_message_id to not nullable
            $table->foreignId('chat_message_id')->nullable(false)->change();
        });
    }
};
