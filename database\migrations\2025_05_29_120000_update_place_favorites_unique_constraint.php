<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        if (DB::getDriverName() === 'pgsql') {
            DB::statement('ALTER TABLE place_favorites DROP CONSTRAINT IF EXISTS place_favorites_user_id_place_id_unique');
        }
    }

    public function down(): void
    {
        if (DB::getDriverName() === 'pgsql') {
            DB::statement('ALTER TABLE place_favorites ADD CONSTRAINT place_favorites_user_id_place_id_unique UNIQUE (user_id, place_id)');
        }
    }
};
