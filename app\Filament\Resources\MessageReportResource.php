<?php

namespace App\Filament\Resources;

use App\Filament\Actions\IsAdminAction;
use App\Filament\Resources\MessageReportResource\Pages;
use App\Filament\Resources\MessageReportResource\RelationManagers;
use App\Models\MessageReport;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class MessageReportResource extends Resource
{
    protected static ?string $model = MessageReport::class;

    protected static ?string $navigationIcon = 'heroicon-o-flag';

    protected static ?string $navigationGroup = 'Chat Management';

    protected static ?int $navigationSort = 4;

    protected static ?string $recordTitleAttribute = 'id';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('user_id')
                    ->relationship('user', 'name')
                    ->required(),
                Forms\Components\Select::make('chat_message_id')
                    ->relationship('chatMessage', 'id')
                    ->nullable(),
                Forms\Components\Select::make('chat_session_id')
                    ->relationship('chatSession', 'id')
                    ->required(),
                Forms\Components\Select::make('agent_id')
                    ->relationship('agent', 'name')
                    ->required(),
                Forms\Components\Textarea::make('report_reason')
                    ->maxLength(65535)
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('Report ID')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('chat_session_id')
                    ->label('Conversation ID')
                    ->searchable()
                    ->sortable()
                    ->url(fn ($record) => $record && $record->chat_session_id ? ChatSessionResource::getUrl('edit', ['record' => $record->chat_session_id]) : null)
                    ->openUrlInNewTab(),

                Tables\Columns\TextColumn::make('user.name')
                    ->label('Username')
                    ->searchable()
                    ->sortable()
                    ->url(fn ($record) => $record && $record->user_id ? UserResource::getUrl('edit', ['record' => $record->user_id]) : null)
                    ->openUrlInNewTab(),

                Tables\Columns\TextColumn::make('report_reason')
                    ->label('Report Content')
                    ->searchable()
                    ->sortable()
                    ->limit(50)
                    ->tooltip(fn ($record) => $record ? $record->report_reason : null),

                Tables\Columns\TextColumn::make('agent.name')
                    ->label('Agent')
                    ->searchable()
                    ->sortable()
                    ->url(fn ($record) => $record && $record->agent_id ? AgentResource::getUrl('edit', ['record' => $record->agent_id]) : null)
                    ->openUrlInNewTab(),

                Tables\Columns\TextColumn::make('chatMessage.message_text')
                    ->label('Reported Message')
                    ->limit(30)
                    ->searchable()
                    ->sortable()
                    ->url(fn ($record) => $record && $record->chat_message_id ? ChatMessageResource::getUrl('view', ['record' => $record->chat_message_id]) : null)
                    ->openUrlInNewTab()
                    ->placeholder('General Report')
                    ->visible(fn ($record) => $record && $record->chat_message_id !== null),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Reported At')
                    ->dateTime('M d, Y H:i')
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label('Last Updated')
                    ->dateTime('M d, Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->defaultSort('created_at', 'desc')
            ->filters([
                Tables\Filters\SelectFilter::make('agent')
                    ->relationship('agent', 'name')
                    ->searchable()
                    ->preload(),

                Tables\Filters\SelectFilter::make('user')
                    ->relationship('user', 'name')
                    ->searchable()
                    ->preload(),

                Tables\Filters\Filter::make('created_at')
                    ->form([
                        Forms\Components\DatePicker::make('created_from')
                            ->label('From Date'),
                        Forms\Components\DatePicker::make('created_until')
                            ->label('To Date'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                            )
                            ->when(
                                $data['created_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                            );
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->icon('heroicon-o-eye'),

                Tables\Actions\EditAction::make()
                    ->icon('heroicon-o-pencil'),

                Tables\Actions\DeleteAction::make()
                    ->icon('heroicon-o-trash'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->emptyStateActions([
                Tables\Actions\CreateAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMessageReports::route('/'),
            'create' => Pages\CreateMessageReport::route('/create'),
            'view' => Pages\ViewMessageReport::route('/{record}'),
            'edit' => Pages\EditMessageReport::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->with(['user', 'chatMessage', 'chatSession', 'agent'])
            ->orderBy('created_at', 'desc');
    }

    public static function canViewAny(): bool
    {
        return IsAdminAction::handle();
    }
}
