<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\AI\AgentOpsOpenAIService;

class TestAgentOpsCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'agentops:test';

    /**
     * The console command description.
     */
    protected $description = 'Test AgentOps integration';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Testing AgentOps Integration...');
        
        // Create AgentOps service
        $agentOpsService = new AgentOpsOpenAIService();
        
        // Check status
        $status = $agentOpsService->getAgentOpsStatus();
        
        $this->info('AgentOps Status:');
        $this->table(
            ['Setting', 'Value'],
            [
                ['Enabled', $status['enabled'] ? 'Yes' : 'No'],
                ['API Key Configured', $status['api_key_configured'] ? 'Yes' : 'No'],
                ['Base URL', $status['base_url']],
                ['Healthy', $status['healthy'] ? 'Yes' : 'No'],
            ]
        );
        
        if (!$status['enabled']) {
            $this->warn('AgentOps is not enabled. Set AGENTOPS_ENABLED=true in your .env file.');
            return;
        }
        
        if (!$status['api_key_configured']) {
            $this->error('AgentOps API key is not configured. Set AGENTOPS_API_KEY in your .env file.');
            return;
        }
        
        if (!$status['healthy']) {
            $this->error('AgentOps API is not accessible. Check your API key and network connection.');
            return;
        }
        
        // Test a simple chat completion
        $this->info('Testing chat completion with AgentOps tracking...');
        
        try {
            $response = $agentOpsService->generateResponse(
                'Hello, this is a test message for AgentOps integration.',
                [
                    'model' => 'gpt-4',
                    'user_id' => 1,
                    'chat_session_id' => 'test-session-' . now()->timestamp,
                    'agent_id' => 1,
                    'temperature' => 0.7,
                    'max_tokens' => 100,
                ]
            );
            
            $this->info('✅ Chat completion successful!');
            $this->info('Response: ' . substr($response, 0, 100) . '...');
            $this->info('Check your AgentOps dashboard at https://app.agentops.ai to see the tracked session.');
            
        } catch (\Exception $e) {
            $this->error('❌ Chat completion failed: ' . $e->getMessage());
        }
    }
}
