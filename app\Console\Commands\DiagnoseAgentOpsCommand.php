<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

class DiagnoseAgentOpsCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'agentops:diagnose';

    /**
     * The console command description.
     */
    protected $description = 'Diagnose AgentOps connectivity issues';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 Diagnosing AgentOps Integration...');
        $this->newLine();
        
        // Check environment configuration
        $this->checkEnvironmentConfig();
        $this->newLine();
        
        // Check network connectivity
        $this->checkNetworkConnectivity();
        $this->newLine();
        
        // Test API key
        $this->testApiKey();
        $this->newLine();
        
        $this->info('✅ Diagnosis complete. Check the results above.');
    }
    
    private function checkEnvironmentConfig()
    {
        $this->info('📋 Environment Configuration:');
        
        $enabled = config('services.agentops.enabled', false);
        $apiKey = config('services.agentops.api_key');
        $baseUrl = config('services.agentops.base_url', 'https://api.agentops.ai');
        
        $this->table(['Setting', 'Value', 'Status'], [
            ['AGENTOPS_ENABLED', $enabled ? 'true' : 'false', $enabled ? '✅' : '❌'],
            ['AGENTOPS_API_KEY', $apiKey ? 'Set (' . strlen($apiKey) . ' chars)' : 'Not set', $apiKey ? '✅' : '❌'],
            ['AGENTOPS_BASE_URL', $baseUrl, '✅'],
        ]);
        
        if (!$enabled) {
            $this->warn('⚠️  AgentOps is disabled. Set AGENTOPS_ENABLED=true in your .env file.');
        }
        
        if (!$apiKey) {
            $this->error('❌ AgentOps API key is not configured. Set AGENTOPS_API_KEY in your .env file.');
        }
    }
    
    private function checkNetworkConnectivity()
    {
        $this->info('🌐 Network Connectivity:');
        
        $baseUrl = config('services.agentops.base_url', 'https://api.agentops.ai');
        
        try {
            $response = Http::timeout(10)->get($baseUrl);
            
            if ($response->successful()) {
                $this->info("✅ Can reach {$baseUrl} (Status: {$response->status()})");
            } else {
                $this->warn("⚠️  Can reach {$baseUrl} but got status: {$response->status()}");
            }
        } catch (\Exception $e) {
            $this->error("❌ Cannot reach {$baseUrl}: " . $e->getMessage());
            $this->info('This could indicate:');
            $this->info('- Network connectivity issues');
            $this->info('- Firewall blocking the request');
            $this->info('- DNS resolution problems');
        }
    }
    
    private function testApiKey()
    {
        $this->info('🔑 API Key Test:');
        
        $apiKey = config('services.agentops.api_key');
        $baseUrl = config('services.agentops.base_url', 'https://api.agentops.ai');
        
        if (!$apiKey) {
            $this->error('❌ No API key configured. Cannot test.');
            return;
        }
        
        try {
            $testSessionId = 'diagnose-' . Str::uuid();
            
            $response = Http::withHeaders([
                'X-Agentops-Api-Key' => $apiKey,
                'Content-Type' => 'application/json',
            ])->timeout(15)->post($baseUrl . '/v2/create_session', [
                'session' => [
                    'id' => $testSessionId,
                    'init_timestamp' => now()->toISOString(),
                    'tags' => ['diagnosis', 'test']
                ]
            ]);
            
            if ($response->successful()) {
                $this->info('✅ API key is valid and working!');
                $this->info("Test session created: {$testSessionId}");
                
                $responseData = $response->json();
                if (isset($responseData['jwt'])) {
                    $this->info('✅ JWT token received');
                }
            } else {
                $this->error("❌ API key test failed (Status: {$response->status()})");
                $this->error('Response: ' . $response->body());
                
                if ($response->status() === 401) {
                    $this->error('This indicates an invalid API key.');
                } elseif ($response->status() === 403) {
                    $this->error('This indicates insufficient permissions.');
                } elseif ($response->status() === 429) {
                    $this->error('This indicates rate limiting.');
                }
            }
            
        } catch (\Exception $e) {
            $this->error('❌ API key test failed with exception: ' . $e->getMessage());
        }
    }
}
