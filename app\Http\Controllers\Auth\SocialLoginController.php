<?php

namespace App\Http\Controllers\Auth;

use App\Actions\Auth\SocialLoginAction;
use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\SocialLoginRequest;
use App\Http\Resources\UserResource;
use Illuminate\Support\Facades\Log;

class SocialLoginController extends Controller
{
    /**
     * Handle social login request
     *
     * @param SocialLoginRequest $request
     * @param SocialLoginAction $socialLoginAction
     * @return UserResource
     */
    public function __invoke(SocialLoginRequest $request, SocialLoginAction $socialLoginAction)
    {
        $user = $socialLoginAction->handle($request->validated());
        
        $user->tokens()->delete();

        return UserResource::make($user)
            ->withToken($request->provider . '-token');
    }
}
