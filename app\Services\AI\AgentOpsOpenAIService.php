<?php

namespace App\Services\AI;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use OpenAI\Client;
use OpenAI\Laravel\Facades\OpenAI;

class AgentOpsOpenAIService implements AIServiceInterface
{
    protected $model;
    protected $agentOpsApiKey;
    protected $agentOpsBaseUrl;
    protected $fallbackService;
    protected $client;

    public function __construct(string $model = 'gpt-4')
    {
        $this->model = $model;
        $this->agentOpsApiKey = config('services.agentops.api_key');
        $this->agentOpsBaseUrl = config('services.agentops.base_url', 'https://api.agentops.ai');

        // Create OpenAI client
        $this->client = $this->createOpenAIClient();

        // Create fallback service for when AgentOps is unavailable
        $this->fallbackService = new OpenAIService($model);
    }

    /**
     * Create OpenAI client
     */
    protected function createOpenAIClient(): Client
    {
        $factory = \OpenAI::factory()
            ->withApiKey(config('openai.api_key'));

        if (config('openai.organization')) {
            $factory = $factory->withOrganization(config('openai.organization'));
        }

        return $factory->make();
    }
    
    /**
     * Generate response using AgentOps REST API tracking
     */
    public function generateResponse(string $prompt, array $options = []): string
    {
        $model = $options['model'] ?? $this->model;
        $userId = $options['user_id'] ?? null;
        $chatMessageId = $options['chat_message_id'] ?? null;
        $chatSessionId = $options['chat_session_id'] ?? null;
        $agentId = $options['agent_id'] ?? null;

        // Build messages array from prompt
        $messages = [
            ['role' => 'user', 'content' => $prompt]
        ];

        // If this is part of a conversation, build full message history
        if ($chatSessionId) {
            $messages = $this->buildConversationHistory($chatSessionId, $prompt);
        }

        // Create or get AgentOps session
        $agentOpsSessionId = $this->createAgentOpsSession($chatSessionId, $userId, $agentId);
        $jwtToken = $this->getJwtToken($agentOpsSessionId);

        $startTime = now();

        try {
            // Make OpenAI API call directly
            $response = $this->client->chat()->create([
                'model' => $model,
                'messages' => $messages,
                'temperature' => $options['temperature'] ?? 0.7,
                'max_tokens' => $options['max_tokens'] ?? 1000,
            ]);

            $endTime = now();
            $content = $response->choices[0]->message->content;

            // Track the LLM call in AgentOps
            $this->trackLLMEvent($jwtToken, [
                'init_timestamp' => $startTime->toISOString(),
                'end_timestamp' => $endTime->toISOString(),
                'model' => $model,
                'prompt' => $messages,
                'completion' => [
                    'role' => 'assistant',
                    'content' => $content
                ],
                'prompt_tokens' => $response->usage->promptTokens,
                'completion_tokens' => $response->usage->completionTokens,
            ]);

            Log::info('AgentOps session tracked', [
                'session_id' => $agentOpsSessionId,
                'chat_session_id' => $chatSessionId,
                'user_id' => $userId,
            ]);

            return $content;

        } catch (\Exception $e) {
            Log::error('OpenAI API call failed, falling back to standard service', [
                'error' => $e->getMessage(),
                'chat_session_id' => $chatSessionId,
            ]);

            // Fallback to standard OpenAI service
            return $this->fallbackService->generateResponse($prompt, $options);
        }
    }
    
    /**
     * Stream response using AgentOps-enabled Python service
     */
    public function streamResponse(string $prompt, callable $callback, array $options = []): void
    {
        $model = $options['model'] ?? $this->model;
        $userId = $options['user_id'] ?? null;
        $chatMessageId = $options['chat_message_id'] ?? null;
        $chatSessionId = $options['chat_session_id'] ?? null;
        $agentId = $options['agent_id'] ?? null;
        
        // Build messages array from prompt
        $messages = [
            ['role' => 'user', 'content' => $prompt]
        ];
        
        // If this is part of a conversation, build full message history
        if ($chatSessionId) {
            $messages = $this->buildConversationHistory($chatSessionId, $prompt);
        }
        
        $payload = [
            'model' => $model,
            'messages' => $messages,
            'temperature' => $options['temperature'] ?? 0.7,
            'max_tokens' => $options['max_tokens'] ?? 1000,
            'session_id' => $chatSessionId ?? Str::uuid(),
            'user_id' => $userId,
            'agent_id' => $agentId,
            'chat_message_id' => $chatMessageId,
        ];
        
        try {
            $response = Http::timeout(120)
                ->withOptions(['stream' => true])
                ->post($this->agentOpsBaseUrl . '/chat/stream', $payload);
            
            if ($response->successful()) {
                $fullResponse = '';
                $body = $response->getBody();
                
                while (!$body->eof()) {
                    $line = $body->read(1024);
                    
                    // Parse Server-Sent Events format
                    if (strpos($line, 'data: ') === 0) {
                        $jsonData = substr($line, 6);
                        $data = json_decode(trim($jsonData), true);
                        
                        if ($data && isset($data['content'])) {
                            $chunk = $data['content'];
                            $fullResponse .= $chunk;
                            
                            // Call the callback with chunk and full response
                            $callback($chunk, $fullResponse);
                            
                            // Check for completion
                            if ($data['type'] === 'completion') {
                                break;
                            }
                        }
                    }
                }
                
                Log::info('AgentOps streaming session completed', [
                    'chat_session_id' => $chatSessionId,
                    'user_id' => $userId,
                    'response_length' => strlen($fullResponse),
                ]);
                
                return;
            }
            
            Log::warning('AgentOps streaming service error, falling back to direct OpenAI', [
                'status' => $response->status(),
            ]);
            
            // Fallback to direct OpenAI streaming
            $this->fallbackService->streamResponse($prompt, $callback, $options);
            
        } catch (\Exception $e) {
            Log::error('AgentOps streaming service exception, falling back to direct OpenAI', [
                'error' => $e->getMessage(),
            ]);
            
            // Fallback to direct OpenAI streaming
            $this->fallbackService->streamResponse($prompt, $callback, $options);
        }
    }
    
    /**
     * Create or get AgentOps session
     */
    private function createAgentOpsSession(?string $chatSessionId, ?int $userId, ?int $agentId): string
    {
        $sessionId = $chatSessionId ?? Str::uuid();
        $cacheKey = "agentops_session_{$sessionId}";

        // Check if session already exists in cache
        if (Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        try {
            $response = Http::withHeaders([
                'X-Agentops-Api-Key' => $this->agentOpsApiKey,
                'Content-Type' => 'application/json',
            ])->post($this->agentOpsBaseUrl . '/v2/create_session', [
                'session' => [
                    'id' => $sessionId,
                    'init_timestamp' => now()->toISOString(),
                    'tags' => array_filter([
                        'rydo-backend',
                        'laravel',
                        $userId ? "user-{$userId}" : null,
                        $agentId ? "agent-{$agentId}" : null,
                    ]),
                    'host_env' => [
                        'OS' => [
                            'OS' => PHP_OS,
                            'PHP Version' => PHP_VERSION,
                        ],
                        'SDK' => [
                            'Laravel Version' => app()->version(),
                            'AgentOps Integration' => 'REST API',
                        ]
                    ]
                ]
            ]);

            if ($response->successful()) {
                // Cache the session ID for 24 hours
                Cache::put($cacheKey, $sessionId, now()->addHours(24));

                Log::info('AgentOps session created', [
                    'session_id' => $sessionId,
                    'chat_session_id' => $chatSessionId,
                    'user_id' => $userId,
                ]);

                return $sessionId;
            }

            Log::warning('Failed to create AgentOps session', [
                'status' => $response->status(),
                'response' => $response->body(),
            ]);

        } catch (\Exception $e) {
            Log::error('AgentOps session creation failed', [
                'error' => $e->getMessage(),
                'session_id' => $sessionId,
            ]);
        }

        return $sessionId;
    }

    /**
     * Get JWT token for AgentOps session
     */
    private function getJwtToken(string $sessionId): ?string
    {
        $cacheKey = "agentops_jwt_{$sessionId}";

        // Check if JWT exists in cache
        if (Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        try {
            $response = Http::withHeaders([
                'X-Agentops-Api-Key' => $this->agentOpsApiKey,
                'Content-Type' => 'application/json',
            ])->post($this->agentOpsBaseUrl . '/v2/reauthorize_jwt', [
                'session_id' => $sessionId
            ]);

            if ($response->successful()) {
                $data = $response->json();
                $jwt = $data['jwt'] ?? null;

                if ($jwt) {
                    // Cache JWT for 23 hours (expires in 24)
                    Cache::put($cacheKey, $jwt, now()->addHours(23));
                    return $jwt;
                }
            }

        } catch (\Exception $e) {
            Log::error('Failed to get AgentOps JWT', [
                'error' => $e->getMessage(),
                'session_id' => $sessionId,
            ]);
        }

        return null;
    }

    /**
     * Track LLM event in AgentOps
     */
    private function trackLLMEvent(?string $jwtToken, array $eventData): void
    {
        if (!$jwtToken) {
            return;
        }

        try {
            $response = Http::withHeaders([
                'Authorization' => "Bearer {$jwtToken}",
                'Content-Type' => 'application/json',
            ])->post($this->agentOpsBaseUrl . '/v2/create_events', [
                'events' => [
                    array_merge([
                        'type' => 'llm',
                    ], $eventData)
                ]
            ]);

            if (!$response->successful()) {
                Log::warning('Failed to track LLM event in AgentOps', [
                    'status' => $response->status(),
                    'response' => $response->body(),
                ]);
            }

        } catch (\Exception $e) {
            Log::error('AgentOps LLM tracking failed', [
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Build conversation history for context
     */
    private function buildConversationHistory(string $chatSessionId, string $newPrompt): array
    {
        // Get recent messages from the chat session
        $messages = DB::table('chat_messages')
            ->where('chat_session_id', $chatSessionId)
            ->orderBy('created_at', 'asc')
            ->limit(20) // Limit to recent messages to avoid token limits
            ->get(['role', 'message_text'])
            ->map(function ($message) {
                return [
                    'role' => $message->role === 'user' ? 'user' : 'assistant',
                    'content' => $message->message_text
                ];
            })
            ->toArray();

        // Add the new prompt
        $messages[] = ['role' => 'user', 'content' => $newPrompt];

        return $messages;
    }
    
    /**
     * Check if AgentOps API is accessible
     */
    public function isAgentOpsHealthy(): bool
    {
        if (!$this->agentOpsApiKey) {
            return false;
        }

        try {
            // Test API connectivity by attempting to create a test session
            $response = Http::withHeaders([
                'X-Agentops-Api-Key' => $this->agentOpsApiKey,
                'Content-Type' => 'application/json',
            ])->timeout(5)->post($this->agentOpsBaseUrl . '/v2/create_session', [
                'session' => [
                    'id' => 'health-check-' . Str::uuid(),
                    'init_timestamp' => now()->toISOString(),
                    'tags' => ['health-check']
                ]
            ]);

            return $response->successful();
        } catch (\Exception $e) {
            Log::debug('AgentOps health check failed', ['error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * Get AgentOps configuration status
     */
    public function getAgentOpsStatus(): array
    {
        return [
            'enabled' => config('services.agentops.enabled', false),
            'api_key_configured' => !empty($this->agentOpsApiKey),
            'base_url' => $this->agentOpsBaseUrl,
            'healthy' => $this->isAgentOpsHealthy(),
        ];
    }
}

