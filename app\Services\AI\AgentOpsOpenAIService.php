<?php

namespace App\Services\AI;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class AgentOpsOpenAIService implements AIServiceInterface
{
    protected $model;
    protected $agentOpsServiceUrl;
    protected $fallbackService;
    
    public function __construct(string $model = 'gpt-4')
    {
        $this->model = $model;
        $this->agentOpsServiceUrl = config('services.agentops.service_url', 'http://localhost:5000');
        
        // Create fallback service for when AgentOps service is unavailable
        $this->fallbackService = new OpenAIService($model);
    }
    
    /**
     * Generate response using AgentOps-enabled Python service
     */
    public function generateResponse(string $prompt, array $options = []): string
    {
        $model = $options['model'] ?? $this->model;
        $userId = $options['user_id'] ?? null;
        $chatMessageId = $options['chat_message_id'] ?? null;
        $chatSessionId = $options['chat_session_id'] ?? null;
        $agentId = $options['agent_id'] ?? null;
        
        // Build messages array from prompt
        $messages = [
            ['role' => 'user', 'content' => $prompt]
        ];
        
        // If this is part of a conversation, build full message history
        if ($chatSessionId) {
            $messages = $this->buildConversationHistory($chatSessionId, $prompt);
        }
        
        $payload = [
            'model' => $model,
            'messages' => $messages,
            'temperature' => $options['temperature'] ?? 0.7,
            'max_tokens' => $options['max_tokens'] ?? 1000,
            'session_id' => $chatSessionId ?? Str::uuid(),
            'user_id' => $userId,
            'agent_id' => $agentId,
            'chat_message_id' => $chatMessageId,
        ];
        
        try {
            $response = Http::timeout(60)
                ->post($this->agentOpsServiceUrl . '/chat/completions', $payload);
            
            if ($response->successful()) {
                $data = $response->json();
                
                // Log AgentOps session info
                Log::info('AgentOps session created', [
                    'session_id' => $data['agentops_session_id'] ?? null,
                    'chat_session_id' => $chatSessionId,
                    'user_id' => $userId,
                ]);
                
                return $data['choices'][0]['message']['content'] ?? '';
            }
            
            Log::warning('AgentOps service error, falling back to direct OpenAI', [
                'status' => $response->status(),
                'response' => $response->body(),
            ]);
            
            // Fallback to direct OpenAI
            return $this->fallbackService->generateResponse($prompt, $options);
            
        } catch (\Exception $e) {
            Log::error('AgentOps service exception, falling back to direct OpenAI', [
                'error' => $e->getMessage(),
                'payload' => $payload
            ]);
            
            // Fallback to direct OpenAI
            return $this->fallbackService->generateResponse($prompt, $options);
        }
    }
    
    /**
     * Stream response using AgentOps-enabled Python service
     */
    public function streamResponse(string $prompt, callable $callback, array $options = []): void
    {
        $model = $options['model'] ?? $this->model;
        $userId = $options['user_id'] ?? null;
        $chatMessageId = $options['chat_message_id'] ?? null;
        $chatSessionId = $options['chat_session_id'] ?? null;
        $agentId = $options['agent_id'] ?? null;
        
        // Build messages array from prompt
        $messages = [
            ['role' => 'user', 'content' => $prompt]
        ];
        
        // If this is part of a conversation, build full message history
        if ($chatSessionId) {
            $messages = $this->buildConversationHistory($chatSessionId, $prompt);
        }
        
        $payload = [
            'model' => $model,
            'messages' => $messages,
            'temperature' => $options['temperature'] ?? 0.7,
            'max_tokens' => $options['max_tokens'] ?? 1000,
            'session_id' => $chatSessionId ?? Str::uuid(),
            'user_id' => $userId,
            'agent_id' => $agentId,
            'chat_message_id' => $chatMessageId,
        ];
        
        try {
            $response = Http::timeout(120)
                ->withOptions(['stream' => true])
                ->post($this->agentOpsServiceUrl . '/chat/stream', $payload);
            
            if ($response->successful()) {
                $fullResponse = '';
                $body = $response->getBody();
                
                while (!$body->eof()) {
                    $line = $body->read(1024);
                    
                    // Parse Server-Sent Events format
                    if (strpos($line, 'data: ') === 0) {
                        $jsonData = substr($line, 6);
                        $data = json_decode(trim($jsonData), true);
                        
                        if ($data && isset($data['content'])) {
                            $chunk = $data['content'];
                            $fullResponse .= $chunk;
                            
                            // Call the callback with chunk and full response
                            $callback($chunk, $fullResponse);
                            
                            // Check for completion
                            if ($data['type'] === 'completion') {
                                break;
                            }
                        }
                    }
                }
                
                Log::info('AgentOps streaming session completed', [
                    'chat_session_id' => $chatSessionId,
                    'user_id' => $userId,
                    'response_length' => strlen($fullResponse),
                ]);
                
                return;
            }
            
            Log::warning('AgentOps streaming service error, falling back to direct OpenAI', [
                'status' => $response->status(),
            ]);
            
            // Fallback to direct OpenAI streaming
            $this->fallbackService->streamResponse($prompt, $callback, $options);
            
        } catch (\Exception $e) {
            Log::error('AgentOps streaming service exception, falling back to direct OpenAI', [
                'error' => $e->getMessage(),
            ]);
            
            // Fallback to direct OpenAI streaming
            $this->fallbackService->streamResponse($prompt, $callback, $options);
        }
    }
    
    /**
     * Build conversation history for context
     */
    private function buildConversationHistory(string $chatSessionId, string $newPrompt): array
    {
        // Get recent messages from the chat session
        $messages = \DB::table('chat_messages')
            ->where('chat_session_id', $chatSessionId)
            ->orderBy('created_at', 'asc')
            ->limit(20) // Limit to recent messages to avoid token limits
            ->get(['role', 'message_text'])
            ->map(function ($message) {
                return [
                    'role' => $message->role === 'user' ? 'user' : 'assistant',
                    'content' => $message->message_text
                ];
            })
            ->toArray();
        
        // Add the new prompt
        $messages[] = ['role' => 'user', 'content' => $newPrompt];
        
        return $messages;
    }
    
    /**
     * Check if AgentOps service is healthy
     */
    public function isAgentOpsHealthy(): bool
    {
        try {
            $response = Http::timeout(5)->get($this->agentOpsServiceUrl . '/health');
            return $response->successful();
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * Get AgentOps service metrics
     */
    public function getAgentOpsMetrics(): array
    {
        try {
            $response = Http::timeout(5)->get($this->agentOpsServiceUrl . '/metrics');
            return $response->successful() ? $response->json() : [];
        } catch (\Exception $e) {
            return [];
        }
    }
}
