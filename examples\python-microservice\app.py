from flask import Flask, request, jsonify, Response
import agentops
from openai import OpenAI
import os
import uuid
import json
import time
from agentops.sdk.decorators import agent, operation

app = Flask(__name__)

# Initialize AgentOps with auto-start disabled for manual session control
agentops.init(
    api_key=os.getenv('AGENTOPS_API_KEY'),
    tags=["rydo-backend", "production"],
    auto_start_session=False
)

# Initialize OpenAI client
client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))

@agent(name="rydo-chat-agent")
class RydoChatAgent:
    """AgentOps-tracked chat agent for Rydo backend"""

    def __init__(self):
        self.client = client

    @operation(name="chat-completion")
    def generate_chat_completion(self, messages, model='gpt-4', **kwargs):
        """Generate chat completion with AgentOps tracking"""
        return self.client.chat.completions.create(
            model=model,
            messages=messages,
            temperature=kwargs.get('temperature', 0.7),
            max_tokens=kwargs.get('max_tokens', 1000)
        )

    @operation(name="chat-stream")
    def generate_chat_stream(self, messages, model='gpt-4', **kwargs):
        """Generate streaming chat completion with AgentOps tracking"""
        return self.client.chat.completions.create(
            model=model,
            messages=messages,
            temperature=kwargs.get('temperature', 0.7),
            max_tokens=kwargs.get('max_tokens', 1000),
            stream=True
        )

# Initialize the agent
chat_agent = RydoChatAgent()

@app.route('/chat/completions', methods=['POST'])
def chat_completions():
    """Handle regular chat completions"""
    try:
        data = request.json

        # Extract Laravel session info
        session_id = data.get('session_id', str(uuid.uuid4()))
        user_id = data.get('user_id')
        agent_id = data.get('agent_id')
        chat_message_id = data.get('chat_message_id')

        # Start AgentOps session with rich metadata
        agentops.start_session(
            tags=[
                f"user-{user_id}",
                f"session-{session_id}",
                f"agent-{agent_id}",
                "chat-completion"
            ]
        )

        # Make OpenAI call through AgentOps-tracked agent
        response = chat_agent.generate_chat_completion(
            messages=data.get('messages', []),
            model=data.get('model', 'gpt-4'),
            temperature=data.get('temperature', 0.7),
            max_tokens=data.get('max_tokens', 1000)
        )

        # End session with success
        agentops.end_session('Success')

        return jsonify({
            'id': response.id,
            'object': response.object,
            'created': response.created,
            'model': response.model,
            'choices': [
                {
                    'index': choice.index,
                    'message': {
                        'role': choice.message.role,
                        'content': choice.message.content
                    },
                    'finish_reason': choice.finish_reason
                } for choice in response.choices
            ],
            'usage': {
                'prompt_tokens': response.usage.prompt_tokens,
                'completion_tokens': response.usage.completion_tokens,
                'total_tokens': response.usage.total_tokens
            },
            'agentops_session_id': session_id
        })

    except Exception as e:
        agentops.end_session('Error')
        return jsonify({'error': str(e)}), 500

@app.route('/chat/stream', methods=['POST'])
def chat_stream():
    """Handle streaming chat completions"""
    try:
        data = request.json

        # Extract Laravel session info
        session_id = data.get('session_id', str(uuid.uuid4()))
        user_id = data.get('user_id')
        agent_id = data.get('agent_id')

        # Start AgentOps session with rich metadata
        agentops.start_session(
            tags=[
                f"user-{user_id}",
                f"session-{session_id}",
                f"agent-{agent_id}",
                "chat-stream"
            ]
        )

        def generate_stream():
            try:
                # Make streaming OpenAI call through AgentOps-tracked agent
                stream = chat_agent.generate_chat_stream(
                    messages=data.get('messages', []),
                    model=data.get('model', 'gpt-4'),
                    temperature=data.get('temperature', 0.7),
                    max_tokens=data.get('max_tokens', 1000)
                )

                full_response = ''
                for chunk in stream:
                    if chunk.choices[0].delta.content is not None:
                        content = chunk.choices[0].delta.content
                        full_response += content

                        # Send chunk in Laravel-compatible format
                        chunk_data = {
                            'id': chunk.id,
                            'content': content,
                            'type': 'text',
                            'sender': 'agent',
                            'timestamp': time.time(),
                            'metadata': {
                                'chat_session_id': session_id,
                                'agent_id': agent_id,
                                'agentops_tracked': True
                            }
                        }

                        yield f"data: {json.dumps(chunk_data)}\n\n"

                # Send completion message
                completion_data = {
                    'id': chunk.id,
                    'content': '',
                    'type': 'completion',
                    'sender': 'agent',
                    'timestamp': time.time(),
                    'metadata': {
                        'completed': True,
                        'chat_session_id': session_id,
                        'agent_id': agent_id,
                        'full_response': full_response,
                        'agentops_tracked': True
                    }
                }
                yield f"data: {json.dumps(completion_data)}\n\n"

                # End AgentOps session
                agentops.end_session('Success')

            except Exception as e:
                agentops.end_session('Error')
                error_data = {
                    'id': str(uuid.uuid4()),
                    'content': '',
                    'type': 'error',
                    'sender': 'system',
                    'timestamp': time.time(),
                    'metadata': {
                        'error': str(e),
                        'chat_session_id': session_id
                    }
                }
                yield f"data: {json.dumps(error_data)}\n\n"

        return Response(
            generate_stream(),
            mimetype='text/event-stream',
            headers={
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type',
            }
        )

    except Exception as e:
        agentops.end_session('Error')
        return jsonify({'error': str(e)}), 500

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'service': 'rydo-agentops-proxy',
        'agentops_initialized': True
    })

@app.route('/metrics', methods=['GET'])
def metrics():
    """Basic metrics endpoint"""
    return jsonify({
        'service': 'rydo-agentops-proxy',
        'agentops_version': agentops.__version__ if hasattr(agentops, '__version__') else 'unknown',
        'openai_configured': bool(os.getenv('OPENAI_API_KEY')),
        'agentops_configured': bool(os.getenv('AGENTOPS_API_KEY'))
    })

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=False)
