<?php

namespace Tests\Feature\Api;

use App\Enums\UserType;
use App\Models\Agent;
use App\Models\User;
use App\Models\UserAgentPreference;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class UserAgentPreferenceBulkTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected Agent $agent1;
    protected Agent $agent2;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a user and agents for testing
        $admin = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => 'password',
            'user_type' => UserType::Admin,
        ]);

        $this->user = User::factory()->create();
        $this->agent1 = Agent::factory()->create([
            'is_visible' => true,
            'is_general' => false,
        ]);
        $this->agent2 = Agent::factory()->create([
            'is_visible' => true,
            'is_general' => false,
        ]);
    }

    /** @test */
    public function unauthenticated_users_cannot_access_user_agent_preferences_endpoints()
    {
        // Test GET endpoint
        $response = $this->getJson('/api/users/interests');
        $response->assertStatus(401);

        // Test POST endpoint
        $response = $this->postJson('/api/users/interests', [
            'interests' => [
                [
                    'agent_id' => $this->agent1->id,
                    'interest_level' => 3,
                ],
            ],
        ]);
        $response->assertStatus(401);
    }

    /** @test */
    public function authenticated_users_can_get_their_preferences()
    {
        // Create some preferences
        UserAgentPreference::factory()->create([
            'user_id' => $this->user->id,
            'agent_id' => $this->agent1->id,
            'interest_level' => 3,
        ]);

        UserAgentPreference::factory()->create([
            'user_id' => $this->user->id,
            'agent_id' => $this->agent2->id,
            'interest_level' => 5,
        ]);

        $response = $this->actingAs($this->user)
            ->getJson('/api/users/interests');

        $response->assertStatus(200)
            ->assertJsonCount(2, 'data')
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'user_id',
                        'agent_id',
                        'interest_level',
                        'created_at',
                        'updated_at',
                    ]
                ]
            ]);
    }

    /** @test */
    public function authenticated_users_can_store_bulk_preferences()
    {
        $response = $this->actingAs($this->user)
            ->postJson('/api/users/interests', [
                'interests' => [
                    [
                        'agent_id' => $this->agent1->id,
                        'interest_level' => 3,
                    ],
                    [
                        'agent_id' => $this->agent2->id,
                        'interest_level' => 5,
                    ],
                ],
            ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Your interests have been updated!',
            ]);

        $this->assertDatabaseHas('user_agent_preferences', [
            'user_id' => $this->user->id,
            'agent_id' => $this->agent1->id,
            'interest_level' => 3,
        ]);

        $this->assertDatabaseHas('user_agent_preferences', [
            'user_id' => $this->user->id,
            'agent_id' => $this->agent2->id,
            'interest_level' => 5,
        ]);
    }

    /** @test */
    public function authenticated_users_can_update_existing_preferences()
    {
        // Create an initial preference
        UserAgentPreference::factory()->create([
            'user_id' => $this->user->id,
            'agent_id' => $this->agent1->id,
            'interest_level' => 2,
        ]);

        $response = $this->actingAs($this->user)
            ->postJson('/api/users/interests', [
                'interests' => [
                    [
                        'agent_id' => $this->agent1->id,
                        'interest_level' => 4, // Updated value
                    ],
                ],
            ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Your interests have been updated!',
            ]);

        $this->assertDatabaseHas('user_agent_preferences', [
            'user_id' => $this->user->id,
            'agent_id' => $this->agent1->id,
            'interest_level' => 4, // Should be updated
        ]);
    }

    /** @test */
    public function validation_fails_with_invalid_data()
    {
        // Test with empty interests array
        $response = $this->actingAs($this->user)
            ->postJson('/api/users/interests', [
                'interests' => [],
            ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['interests']);

        // Test with missing agent_id
        $response = $this->actingAs($this->user)
            ->postJson('/api/users/interests', [
                'interests' => [
                    [
                        'interest_level' => 3,
                    ],
                ],
            ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['interests.0.agent_id']);

        // Test with invalid interest_level (too high)
        $response = $this->actingAs($this->user)
            ->postJson('/api/users/interests', [
                'interests' => [
                    [
                        'agent_id' => $this->agent1->id,
                        'interest_level' => 6,
                    ],
                ],
            ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['interests.0.interest_level']);
    }
}
