<?php

namespace App\Actions\Recommendations;

use App\Models\Agent;
use App\Models\ChatMessage;
use App\Models\Place;
use App\Models\Recommendation;
use App\Models\SystemSetting;
use App\Models\User;
use App\Models\UserRecommendationUsage;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class GetRecommendationsAction
{
    /**
     * Get recommendations based on agent and create a recommendation record
     *
     * @param Agent $agent The agent to get recommendations for
     * @param User $user The user to create the recommendation for
     * @param ChatMessage $chatMessage The chat message associated with the recommendation
     * @param int|null $limit The maximum number of recommendations to return
     * @param int|null $categoryId The specific category ID to filter places by
     * @param bool $checkLimit Whether to check the user's recommendation limit
     * @return array The recommendation data including the recommendation record ID and formatted text
     */
    public function execute(Agent $agent, User $user, ChatMessage $chatMessage, ?int $limit = null, ?int $categoryId = null, bool $checkLimit = true): array
    {
        try {
            // Get the maximum number of recommendations from system settings
            $maxRecommendations = SystemSetting::getValue(
                SystemSetting::KEY_MAX_RECOMMENDATIONS,
                5
            );

            // If limit is provided and less than max, use it, otherwise use max
            $limit = ($limit && $limit < $maxRecommendations) ? $limit : $maxRecommendations;

            // Check if the user has enough remaining quota for the requested number of places
            if ($checkLimit && !$user->is_anonymous && !$user->hasActiveSubscription()) {
                $remainingCount = $user->getRemainingRecommendationCount();

                // If user doesn't have enough quota for any recommendations
                if ($remainingCount <= 0) {
                    // Get limit information
                    $limitInfo = app(CheckRecommendationLimitAction::class)->execute($user);

                    // Return limited response
                    return [
                        'limited' => true,
                        'recommendation_id' => null,
                        'formatted_text' => "Get rydo+ or wait " .
                           ceil(now()->diffInDays($limitInfo['next_reset'])) . " days to get more.",
                        'limit_info' => $limitInfo,
                        'places' => []
                    ];
                }

                // Adjust the limit to not exceed the user's remaining quota
                $limit = min($limit, $remainingCount);
            }

            $category = $agent->categories()->find($categoryId);

            $placeQuery = $category
                ? $category->places()
                : $agent->places();

            $placeIds = $placeQuery
                ->active()
                ->inRandomOrder()
                ->limit($limit)
                ->pluck('id')
                ->toArray();

            // Create a recommendation record
            $recommendation = Recommendation::create([
                'user_id' => $user->id,
                'old_place_id' => $placeIds[0] ?? null, // For backward compatibility
                'places_ids' => $placeIds,
                'chat_message_id' => $chatMessage->id,
                'agent_id' => $agent->id,
                'credit_cost' => 0.00, // Default credit cost
            ]);

            // Fetch places in a single query with eager loading for better performance
            $places = Place::with(['category', 'media'])
                ->whereIn('id', $placeIds)
                ->get();

            Log::info('Places fetched for recommendation', [
                'agent_id' => $agent->id,
                'places_count' => $places->count(),
                'category_id' => $categoryId
            ]);

            // Format the places as a text string
            $formattedText = $places->map(function ($place) {
                $name = is_array($place->name) ? ($place->name['en'] ?? '') : $place->name;
                return "• {$name}";
            })->implode("\n");

            // Format the recommendations for the response
            $placesData = $places->map(function ($place) {
                return [
                    'id' => $place->id,
                    'name' => is_array($place->name) ? ($place->name['en'] ?? '') : $place->name,
                    'brief_description' => $place->brief_description,
                    'image_url' => $place->getFirstMediaUrl('place_photos'),
                    'category' => $place->category ? $place->category->name : null
                ];
            })->toArray();

            // Prepare the response data
            $responseData = [
                'recommendation_id' => $recommendation->id,
                'formatted_text' => $formattedText,
                'places' => $placesData
            ];

            // Include the category ID if it was used for filtering
            if ($categoryId) {
                $responseData['category_id'] = $categoryId;
            }

            // Increment the user's recommendation usage count by the number of places returned (if not anonymous)
            if ($checkLimit && !$user->is_anonymous) {
                $placesCount = $places->count();
                $usage = $user->incrementRecommendationUsageByCount($placesCount);

                Log::info('Chat recommendation usage incremented', [
                    'user_id' => $user->id,
                    'places_count' => $placesCount,
                    'new_total_count' => $usage->count,
                    'agent_id' => $agent->id
                ]);

                // Add usage information to the response
                $responseData['limit_info'] = [
                    'limited' => false,
                    'remaining' => max(0, SystemSetting::getValue(SystemSetting::KEY_RECOMMENDATION_LIMIT_REGISTERED, 10) - $usage->count),
                    'limit' => SystemSetting::getValue(SystemSetting::KEY_RECOMMENDATION_LIMIT_REGISTERED, 10),
                    'next_reset' => $usage->next_reset_at,
                ];
            }

            return $responseData;

        } catch (\Exception $e) {
            Log::error('Error getting recommendations from database', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'agent_id' => $agent->id
            ]);
            return [
                'recommendation_id' => null,
                'formatted_text' => '',
                'places' => []
            ];
        }
    }

}
