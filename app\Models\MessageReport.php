<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class MessageReport extends Model
{
    use HasFactory, SoftDeletes;

    protected $guarded = [];

    protected $casts = [
        'deleted_at' => 'datetime',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function chatMessage(): BelongsTo
    {
        return $this->belongsTo(ChatMessage::class);
    }

    public function chatSession(): BelongsTo
    {
        return $this->belongsTo(ChatSession::class);
    }

    public function agent(): BelongsTo
    {
        return $this->belongsTo(Agent::class);
    }

    /**
     * Get the conversation ID (chat session ID)
     */
    public function getConversationIdAttribute(): int
    {
        return $this->chat_session_id;
    }

    /**
     * Get the user's mobile number
     */
    public function getUserMobileAttribute(): ?string
    {
        return $this->user->mobile ?? $this->user->phone;
    }

    /**
     * Get the username
     */
    public function getUsernameAttribute(): string
    {
        return $this->user->name ?? 'Unknown User';
    }
}
