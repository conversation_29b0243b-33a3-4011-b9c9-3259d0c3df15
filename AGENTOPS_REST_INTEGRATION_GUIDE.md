# AgentOps REST API Integration Guide for Rydo Backend

This guide provides complete instructions for integrating AgentOps directly with your Laravel + OpenAI application using the AgentOps REST API.

## Overview

AgentOps integration uses direct REST API calls:
```
Laravel App → OpenAI API → AgentOps REST API → AgentOps Dashboard
```

**Benefits of REST API Approach:**
- ✅ No additional microservices needed
- ✅ Direct integration into Laravel
- ✅ Automatic fallback to standard OpenAI
- ✅ Full AgentOps features (session tracking, cost monitoring, debugging)
- ✅ Production-ready with caching and error handling

## Prerequisites

1. **AgentOps Account**: Sign up at [agentops.ai](https://agentops.ai) and get your API key
2. **Laravel Application**: Your existing Laravel + OpenAI setup

## Setup Instructions

### 1. Configure Laravel Environment

Add these variables to your `.env` file:

```env
# AgentOps Configuration
AGENTOPS_ENABLED=true
AGENTOPS_API_KEY=your-agentops-api-key
AGENTOPS_BASE_URL=https://api.agentops.ai
AGENTOPS_FALLBACK_TO_OPENAI=true
```

### 2. Test the Integration

Run the test command to verify everything is working:

```bash
php artisan agentops:test
```

This will:
- Check AgentOps configuration
- Test API connectivity
- Send a test message through AgentOps
- Show you where to view the results

### 3. Enable AgentOps in Your Application

Your existing Laravel chat functionality will automatically use AgentOps when enabled. The integration is already built into your `AIServiceFactory`.

## How It Works

### Automatic Integration

When `AGENTOPS_ENABLED=true`, your existing Laravel controllers automatically use AgentOps:

1. **Session Creation**: Each chat session creates an AgentOps session
2. **LLM Tracking**: All OpenAI API calls are tracked with full context
3. **Cost Monitoring**: Token usage and costs are automatically calculated
4. **Error Handling**: Automatic fallback to direct OpenAI if AgentOps fails

### What Gets Tracked

- **Chat Sessions**: Each conversation is tracked as an AgentOps session
- **User Context**: User ID, agent ID, and session metadata
- **LLM Calls**: Complete prompt/response pairs with timing
- **Token Usage**: Prompt tokens, completion tokens, and costs
- **Environment Info**: Laravel version, PHP version, and system details

## AgentOps Dashboard Features

### Session Tracking
- View all chat sessions from your Rydo app
- See conversation flow and agent interactions
- Track user engagement patterns

### Cost Analysis
- Monitor OpenAI spending per session
- Track costs by user, agent, or time period
- Identify expensive operations

### Performance Monitoring
- Response times for each LLM call
- Success/failure rates
- API usage patterns

### Debugging Tools
- Session replay functionality
- Complete conversation history
- Error tracking and analysis

## Configuration Options

### Laravel Configuration

In `config/services.php`:

```php
'agentops' => [
    'enabled' => env('AGENTOPS_ENABLED', false),
    'api_key' => env('AGENTOPS_API_KEY'),
    'base_url' => env('AGENTOPS_BASE_URL', 'https://api.agentops.ai'),
    'fallback_to_openai' => env('AGENTOPS_FALLBACK_TO_OPENAI', true),
],
```

### Caching

The integration uses Laravel's cache for:
- **Session IDs**: Cached for 24 hours
- **JWT Tokens**: Cached for 23 hours (expire in 24)
- **Performance**: Reduces API calls to AgentOps

## Production Deployment

### Environment Variables

```env
# Production settings
AGENTOPS_ENABLED=true
AGENTOPS_API_KEY=your-production-api-key
AGENTOPS_BASE_URL=https://api.agentops.ai
AGENTOPS_FALLBACK_TO_OPENAI=true
```

### Monitoring

Monitor these Laravel logs for AgentOps activity:
- `AgentOps session created`
- `AgentOps session tracked`
- `Failed to create AgentOps session`
- `AgentOps LLM tracking failed`

### Error Handling

The integration includes robust error handling:
- **API Failures**: Automatic fallback to direct OpenAI
- **Network Issues**: Graceful degradation
- **Invalid Responses**: Logged but don't break chat functionality

## Troubleshooting

### Common Issues

1. **AgentOps Not Tracking**:
   ```bash
   php artisan agentops:test
   ```
   Check API key and connectivity.

2. **Sessions Not Appearing**:
   - Verify `AGENTOPS_ENABLED=true`
   - Check Laravel logs for errors
   - Ensure API key is correct

3. **Performance Issues**:
   - Check cache configuration
   - Monitor API response times
   - Consider increasing timeout values

### Debug Commands

```bash
# Test AgentOps integration
php artisan agentops:test

# Clear AgentOps cache
php artisan cache:forget agentops_*

# Check Laravel logs
tail -f storage/logs/laravel.log | grep -i agentops
```

## Benefits for Your Rydo App

### Immediate Benefits
- **Zero Code Changes**: Existing controllers work unchanged
- **Comprehensive Tracking**: Every AI interaction is monitored
- **Cost Optimization**: Identify and reduce expensive operations
- **Better Debugging**: Understand AI behavior patterns

### Long-term Benefits
- **User Analytics**: Understand how users interact with AI
- **Performance Optimization**: Identify bottlenecks and improvements
- **Quality Assurance**: Monitor AI response quality
- **Business Intelligence**: Data-driven decisions about AI features

## Next Steps

1. **Enable AgentOps**: Set `AGENTOPS_ENABLED=true`
2. **Test Integration**: Run `php artisan agentops:test`
3. **Monitor Dashboard**: Check [app.agentops.ai](https://app.agentops.ai)
4. **Analyze Data**: Use insights to optimize your AI features

## Support

- **AgentOps Documentation**: [docs.agentops.ai](https://docs.agentops.ai)
- **AgentOps Support**: Contact through their dashboard
- **Laravel Integration**: Check Laravel logs for detailed error messages

The integration is designed to be **production-ready** and **non-intrusive**. Your existing Laravel application continues working exactly as before, but now with comprehensive AgentOps monitoring and observability.
