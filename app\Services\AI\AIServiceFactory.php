<?php

namespace App\Services\AI;

use Illuminate\Support\Facades\Log;

class AIServiceFactory
{
    public static function create(?string $provider = null): AIServiceInterface
    {
        /** @todo enhance to use face for mock test $provider */
//        if (app()->bound(AIServiceInterface::class)) {
//            return app(AIServiceInterface::class);
//        }

        $provider = $provider ?? config('services.ai.default_provider', 'openai');

        /** @todo enhance to choose provider ai based agent provider */
        return match ($provider) {
            'openai' => self::createOpenAIService(),
            'agentops' => new AgentOpsOpenAIService(),
            // Add more providers here as needed
            // 'anthropic' => new AnthropicService(),
            // 'gemini' => new GeminiService(),
            default => throw new \InvalidArgumentException("Unsupported AI provider: {$provider}"),
        };
    }

    /**
     * Create OpenAI service with AgentOps integration if enabled
     */
    private static function createOpenAIService(): AIServiceInterface
    {
        // Check if AgentOps is enabled and service is healthy
        if (config('services.agentops.enabled', false)) {
            $agentOpsService = new AgentOpsOpenAIService();

            // Check if AgentOps service is healthy
            if ($agentOpsService->isAgentOpsHealthy()) {
                return $agentOpsService;
            }

            // Log that AgentOps is enabled but service is unhealthy
            Log::warning('AgentOps is enabled but service is unhealthy, falling back to direct OpenAI');
        }

        // Return standard OpenAI service
        return new OpenAIService();
    }
}
